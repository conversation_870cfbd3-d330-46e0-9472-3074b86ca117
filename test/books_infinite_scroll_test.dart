import 'package:e_library/features/books/ui/components/books_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BooksList Infinite Scroll', () {
    testWidgets('should show loading indicator initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BooksList(),
          ),
        ),
      );

      // Should show loading indicator initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show error state when API fails', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BooksList(),
          ),
        ),
      );

      // Wait for the API call to complete (will fail in test environment)
      await tester.pumpAndSettle();

      // Should show error state
      expect(find.text('Failed to load books'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should have scroll controller attached', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BooksList(),
          ),
        ),
      );

      // Wait for initial load
      await tester.pumpAndSettle();

      // The ListView should be present (even in error state, it's wrapped in RefreshIndicator)
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });
  });
}
