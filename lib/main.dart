import 'package:e_library/core/navigation_service.dart';
import 'package:e_library/features/auth/data/auth_service.dart';
import 'package:e_library/features/auth/ui/screens/login_screen.dart';
import 'package:e_library/features/auth/ui/screens/register_screen.dart';
import 'package:e_library/features/books/ui/screens/books_screen.dart';
import 'package:fl_query/fl_query.dart';
import 'package:fl_query_connectivity_plus_adapter/fl_query_connectivity_plus_adapter.dart';
import 'package:flutter/material.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await QueryClient.initialize(
    connectivity: FlQueryConnectivityPlusAdapter(),
    cachePrefix: 'e_library_cache',
  );

  await AuthService.initializeAuth();

  // Set up callback for handling 401 unauthorized responses
  AuthService.setOnUnauthorizedCallback(() {
    // Navigate to login screen when unauthorized
    NavigationService.navigateToAndClearStack('/login');
  });

  runApp(const ELibrary());
}

class ELibrary extends StatelessWidget {
  const ELibrary({super.key});

  @override
  Widget build(BuildContext context) {
    return QueryClientProvider(
      child: MaterialApp(
        title: 'E-Library',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          useMaterial3: true,
        ),
        navigatorKey: NavigationService.navigatorKey,
        initialRoute: '/',
        routes: {
          '/': (context) => const AuthWrapper(),
          '/login': (context) => const LoginScreen(),
          '/register': (context) => const RegisterScreen(),
          '/books': (context) => const BooksScreen(title: 'E-Library'),
        },
      ),
    );
  }
}

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: AuthService.isAuthenticated(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final isAuthenticated = snapshot.data ?? false;

        if (isAuthenticated) {
          return const BooksScreen(title: 'E-Library');
        } else {
          return const LoginScreen();
        }
      },
    );
  }
}
