import 'package:e_library/features/auth/data/api.dart';
import 'package:e_library/features/auth/data/auth_service.dart';
import 'package:e_library/features/books/ui/components/books_list.dart';
import 'package:flutter/material.dart';

class BooksScreen extends StatefulWidget {
  const BooksScreen({super.key, required this.title});

  final String title;

  @override
  State<BooksScreen> createState() => _BooksScreenState();
}

class _BooksScreenState extends State<BooksScreen> {
  Key _booksListKey = UniqueKey();

  Future<void> _handleLogout() async {
    try {
      try {
        await AuthApi.logout();
      } finally {
        await AuthService.removeToken();
      }

      if (mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
      }
    } catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Logout failed: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: Text(widget.title),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'toggle_data':
                  _toggleDataSource();
                  break;
                case 'logout':
                  _handleLogout();
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'toggle_data',
                child: Row(
                  children: [
                    Icon(BooksService.isUsingFillerData ? Icons.cloud : Icons.storage),
                    const SizedBox(width: 8),
                    Text(BooksService.isUsingFillerData ? 'Use API Data' : 'Use Filler Data'),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    const Icon(Icons.logout),
                    const SizedBox(width: 8),
                    const Text('Logout'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          if (BooksService.isUsingFillerData)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(8),
              color: Theme.of(context).colorScheme.secondaryContainer,
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSecondaryContainer,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Using filler data (API unavailable)',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSecondaryContainer,
                    ),
                  ),
                ],
              ),
            ),
          Expanded(
            child: BooksList(key: _booksListKey),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        tooltip: 'Add Book',
        onPressed: () {},
        child: const Icon(Icons.add),
      ),
    );
  }

  void _toggleDataSource() {
    setState(() {
      BooksService.setFillerDataMode(!BooksService.isUsingFillerData);
      // Force rebuild of BooksList by changing its key
      _booksListKey = UniqueKey();
    });

    // Show a snackbar to inform the user
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          BooksService.isUsingFillerData
            ? 'Switched to filler data'
            : 'Switched to API data',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
