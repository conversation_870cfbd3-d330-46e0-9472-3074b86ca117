import 'package:dio/dio.dart';
import 'package:e_library/features/api/dio.dart';
import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/models/book.dart';

/// API service for managing books
class BooksApi {
  /// Get paginated list of books
  static Future<PaginatedResponse<List<Book>>> getPaginatedBooks() async {
    try {
      final response = await dio.get('/books');
      return PaginatedResponse.fromJsonList(
        response.data as Map<String, dynamic>,
        Book.fromJson,
      );
    } on DioException catch (e) {
      throw BooksApiException('Failed to fetch books: ${e.message}');
    }
  }

  /// Get all books (non-paginated)
  static Future<List<Book>> getAllBooks() async {
    try {
      final response = await dio.get('/books');
      final data = response.data as Map<String, dynamic>;
      final booksData = data['data'] as List<dynamic>;
      return booksData
          .map((bookJson) => Book.fromJson(bookJson as Map<String, dynamic>))
          .toList();
    } on DioException catch (e) {
      throw BooksApiException('Failed to fetch books: ${e.message}');
    }
  }

  /// Get a specific book by ID
  static Future<Book> getBook(int id) async {
    try {
      final response = await dio.get('/books/$id');
      final data = response.data as Map<String, dynamic>;
      return Book.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw BooksApiException('Book not found');
      }
      throw BooksApiException('Failed to fetch book: ${e.message}');
    }
  }

  /// Create a new book
  static Future<Book> createBook({
    required String title,
    required String category,
    required double price,
    required int publisherId,
    required int authorId,
  }) async {
    try {
      final response = await dio.post('/books', data: {
        'title': title,
        'category': category,
        'price': price,
        'publisher_id': publisherId,
        'author_id': authorId,
      });
      final data = response.data as Map<String, dynamic>;
      return Book.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw BooksApiException(errorMessage);
      }
      throw BooksApiException('Failed to create book: ${e.message}');
    }
  }

  /// Update an existing book
  static Future<Book> updateBook(
    int id, {
    required String title,
    required String category,
    required double price,
    required int publisherId,
    required int authorId,
  }) async {
    try {
      final response = await dio.put('/books/$id', data: {
        'title': title,
        'category': category,
        'price': price,
        'publisher_id': publisherId,
        'author_id': authorId,
      });
      final data = response.data as Map<String, dynamic>;
      return Book.fromJson(data['data'] as Map<String, dynamic>);
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw BooksApiException('Book not found');
      }
      if (e.response?.statusCode == 422) {
        final errors = e.response?.data['errors'] as Map<String, dynamic>?;
        final errorMessage = _formatValidationErrors(errors);
        throw BooksApiException(errorMessage);
      }
      throw BooksApiException('Failed to update book: ${e.message}');
    }
  }

  /// Delete a book
  static Future<void> deleteBook(int id) async {
    try {
      await dio.delete('/books/$id');
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        throw BooksApiException('Book not found');
      }
      throw BooksApiException('Failed to delete book: ${e.message}');
    }
  }

  /// Format validation errors into a readable string
  static String _formatValidationErrors(Map<String, dynamic>? errors) {
    if (errors == null) return 'Validation failed';

    final errorMessages = <String>[];
    errors.forEach((field, messages) {
      if (messages is List) {
        errorMessages.addAll(messages.cast<String>());
      }
    });

    return errorMessages.isNotEmpty
        ? errorMessages.join('\n')
        : 'Validation failed';
  }
}

/// Exception thrown by BooksApi
class BooksApiException implements Exception {
  final String message;

  const BooksApiException(this.message);

  @override
  String toString() => message;
}

/// Legacy function for backward compatibility
Future<PaginatedResponse<List<Book>>> getPaginatedBooks() async {
  return BooksApi.getPaginatedBooks();
}
