import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/api.dart';
import 'package:e_library/features/books/data/authors_api.dart';
import 'package:e_library/features/books/data/publishers_api.dart';
import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/books/data/models/publisher.dart';

/// Service that provides books, authors, and publishers data from API
class BooksService {

  // BOOKS

  /// Get paginated books (includes nested author/publisher from API)
  static Future<PaginatedResponse<List<Book>>> getPaginatedBooks() async {
    return await BooksApi.getPaginatedBooks();
  }

  /// Get all books (includes nested author/publisher from API)
  static Future<List<Book>> getAllBooks() async {
    return await BooksApi.getAllBooks();
  }

  // AUTHORS

  /// Get all authors
  static Future<List<Author>> getAllAuthors() async {
    return await AuthorsApi.getAllAuthors();
  }

  /// Get author by ID
  static Future<Author> getAuthor(int id) async {
    return await AuthorsApi.getAuthor(id);
  }

  // PUBLISHERS

  /// Get all publishers
  static Future<List<Publisher>> getAllPublishers() async {
    return await PublishersApi.getAllPublishers();
  }

  /// Get publisher by ID
  static Future<Publisher> getPublisher(int id) async {
    return await PublishersApi.getPublisher(id);
  }

  // CATEGORIES

  /// Get all unique book categories
  static Future<List<String>> getCategories() async {
    final books = await BooksApi.getAllBooks();
    return books.map((book) => book.category).toSet().toList()..sort();
  }
}
