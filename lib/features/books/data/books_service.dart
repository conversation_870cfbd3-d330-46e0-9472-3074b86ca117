import 'package:e_library/core/data/filler_data.dart';
import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/api.dart';
import 'package:e_library/features/books/data/authors_api.dart';
import 'package:e_library/features/books/data/publishers_api.dart';
import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/books/data/models/publisher.dart';

/// Service that provides books, authors, and publishers data
/// Falls back to filler data when API is unavailable
class BooksService {
  static bool _useFillerData = false;

  /// Enable or disable filler data mode
  static void setFillerDataMode(bool enabled) {
    _useFillerData = enabled;
  }

  /// Check if currently using filler data
  static bool get isUsingFillerData => _useFillerData;

  // BOOKS

  /// Get paginated books (now includes nested author/publisher from API)
  static Future<PaginatedResponse<List<Book>>> getPaginatedBooks() async {
    if (_useFillerData) {
      return _getFillerBooks();
    }

    try {
      return await BooksApi.getPaginatedBooks();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return _getFillerBooks();
    }
  }

  /// Get all books (now includes nested author/publisher from API)
  static Future<List<Book>> getAllBooks() async {
    if (_useFillerData) {
      return FillerData.books;
    }

    try {
      return await BooksApi.getAllBooks();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return FillerData.books;
    }
  }

  // AUTHORS

  /// Get all authors
  static Future<List<Author>> getAllAuthors() async {
    if (_useFillerData) {
      return FillerData.authors;
    }

    try {
      return await AuthorsApi.getAllAuthors();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return FillerData.authors;
    }
  }

  /// Get author by ID
  static Future<Author?> getAuthor(int id) async {
    if (_useFillerData) {
      return FillerData.getAuthorById(id);
    }

    try {
      return await AuthorsApi.getAuthor(id);
    } catch (e) {
      // Fallback to filler data on API error
      return FillerData.getAuthorById(id);
    }
  }

  // PUBLISHERS

  /// Get all publishers
  static Future<List<Publisher>> getAllPublishers() async {
    if (_useFillerData) {
      return FillerData.publishers;
    }

    try {
      return await PublishersApi.getAllPublishers();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return FillerData.publishers;
    }
  }

  /// Get publisher by ID
  static Future<Publisher?> getPublisher(int id) async {
    if (_useFillerData) {
      return FillerData.getPublisherById(id);
    }

    try {
      return await PublishersApi.getPublisher(id);
    } catch (e) {
      // Fallback to filler data on API error
      return FillerData.getPublisherById(id);
    }
  }

  // CATEGORIES

  /// Get all unique book categories
  static Future<List<String>> getCategories() async {
    if (_useFillerData) {
      return FillerData.categories;
    }

    try {
      final books = await BooksApi.getAllBooks();
      return books.map((book) => book.category).toSet().toList()..sort();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return FillerData.categories;
    }
  }

  // PRIVATE HELPER METHODS

  static PaginatedResponse<List<Book>> _getFillerBooks() {
    final books = FillerData.books;

    return PaginatedResponse(
      data: books,
      links: PaginationLinks(first: null, last: null, prev: null, next: null),
      meta: PaginationMeta(
        currentPage: 1,
        from: 1,
        lastPage: 1,
        links: [],
        path: '',
        perPage: books.length,
        to: books.length,
        total: books.length,
      ),
    );
  }
}
