import 'package:e_library/core/data/filler_data.dart';
import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/api.dart';
import 'package:e_library/features/books/data/authors_api.dart';
import 'package:e_library/features/books/data/publishers_api.dart';
import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/books/data/models/publisher.dart';

/// Service that provides books, authors, and publishers data
/// Falls back to filler data when API is unavailable
class BooksService {
  static bool _useFillerData = false;

  /// Enable or disable filler data mode
  static void setFillerDataMode(bool enabled) {
    _useFillerData = enabled;
  }

  /// Check if currently using filler data
  static bool get isUsingFillerData => _useFillerData;

  // BOOKS

  /// Get paginated books with enhanced information
  static Future<PaginatedResponse<List<BookWithDetails>>> getPaginatedBooksWithDetails() async {
    if (_useFillerData) {
      return _getFillerBooksWithDetails();
    }

    try {
      final booksResponse = await BooksApi.getPaginatedBooks();
      final booksWithDetails = <BookWithDetails>[];

      for (final book in booksResponse.data) {
        final author = await _getAuthorSafely(book.authorId);
        final publisher = await _getPublisherSafely(book.publisherId);
        
        booksWithDetails.add(BookWithDetails(
          book: book,
          author: author,
          publisher: publisher,
        ));
      }

      return PaginatedResponse(
        data: booksWithDetails,
        links: booksResponse.links,
        meta: booksResponse.meta,
      );
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return _getFillerBooksWithDetails();
    }
  }

  /// Get all books with details
  static Future<List<BookWithDetails>> getAllBooksWithDetails() async {
    if (_useFillerData) {
      return _getAllFillerBooksWithDetails();
    }

    try {
      final books = await BooksApi.getAllBooks();
      final booksWithDetails = <BookWithDetails>[];

      for (final book in books) {
        final author = await _getAuthorSafely(book.authorId);
        final publisher = await _getPublisherSafely(book.publisherId);
        
        booksWithDetails.add(BookWithDetails(
          book: book,
          author: author,
          publisher: publisher,
        ));
      }

      return booksWithDetails;
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return _getAllFillerBooksWithDetails();
    }
  }

  // AUTHORS

  /// Get all authors
  static Future<List<Author>> getAllAuthors() async {
    if (_useFillerData) {
      return FillerData.authors;
    }

    try {
      return await AuthorsApi.getAllAuthors();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return FillerData.authors;
    }
  }

  /// Get author by ID
  static Future<Author?> getAuthor(int id) async {
    if (_useFillerData) {
      return FillerData.getAuthorById(id);
    }

    try {
      return await AuthorsApi.getAuthor(id);
    } catch (e) {
      // Fallback to filler data on API error
      return FillerData.getAuthorById(id);
    }
  }

  // PUBLISHERS

  /// Get all publishers
  static Future<List<Publisher>> getAllPublishers() async {
    if (_useFillerData) {
      return FillerData.publishers;
    }

    try {
      return await PublishersApi.getAllPublishers();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return FillerData.publishers;
    }
  }

  /// Get publisher by ID
  static Future<Publisher?> getPublisher(int id) async {
    if (_useFillerData) {
      return FillerData.getPublisherById(id);
    }

    try {
      return await PublishersApi.getPublisher(id);
    } catch (e) {
      // Fallback to filler data on API error
      return FillerData.getPublisherById(id);
    }
  }

  // CATEGORIES

  /// Get all unique book categories
  static Future<List<String>> getCategories() async {
    if (_useFillerData) {
      return FillerData.categories;
    }

    try {
      final books = await BooksApi.getAllBooks();
      return books.map((book) => book.category).toSet().toList()..sort();
    } catch (e) {
      // Fallback to filler data on API error
      _useFillerData = true;
      return FillerData.categories;
    }
  }

  // PRIVATE HELPER METHODS

  static Future<Author?> _getAuthorSafely(int authorId) async {
    try {
      return await AuthorsApi.getAuthor(authorId);
    } catch (e) {
      return FillerData.getAuthorById(authorId);
    }
  }

  static Future<Publisher?> _getPublisherSafely(int publisherId) async {
    try {
      return await PublishersApi.getPublisher(publisherId);
    } catch (e) {
      return FillerData.getPublisherById(publisherId);
    }
  }

  static PaginatedResponse<List<BookWithDetails>> _getFillerBooksWithDetails() {
    final booksWithDetails = _getAllFillerBooksWithDetails();
    
    return PaginatedResponse(
      data: booksWithDetails,
      links: PaginationLinks(first: null, last: null, prev: null, next: null),
      meta: PaginationMeta(
        currentPage: 1,
        from: 1,
        lastPage: 1,
        links: [],
        path: '',
        perPage: booksWithDetails.length,
        to: booksWithDetails.length,
        total: booksWithDetails.length,
      ),
    );
  }

  static List<BookWithDetails> _getAllFillerBooksWithDetails() {
    final books = FillerData.books;
    return books.map((book) {
      final author = FillerData.getAuthorById(book.authorId);
      final publisher = FillerData.getPublisherById(book.publisherId);
      
      return BookWithDetails(
        book: book,
        author: author,
        publisher: publisher,
      );
    }).toList();
  }
}

/// Enhanced book model with author and publisher details
class BookWithDetails {
  final Book book;
  final Author? author;
  final Publisher? publisher;

  const BookWithDetails({
    required this.book,
    this.author,
    this.publisher,
  });

  /// Get author name or fallback
  String get authorName => author?.fullName ?? 'Unknown Author';

  /// Get publisher name or fallback
  String get publisherName => publisher?.name ?? 'Unknown Publisher';

  /// Get full display information
  String get fullDetails => '${book.title} by $authorName (${book.category})';
}
