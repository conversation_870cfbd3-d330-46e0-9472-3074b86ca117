import 'package:json_annotation/json_annotation.dart';

part 'book.g.dart';

@JsonSerializable()
class Book {
  final int id;
  final String title;
  final String category;
  final double price;

  @<PERSON><PERSON><PERSON>ey(name: 'publisher_id')
  final int publisherId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'author_id')
  final int authorId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String? createdAt;

  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  const Book({
    required this.id,
    required this.title,
    required this.category,
    required this.price,
    required this.publisherId,
    required this.authorId,
    this.createdAt,
    this.updatedAt,
  });

  /// Creates a Book from JSON data
  factory Book.fromJson(Map<String, dynamic> json) => _$BookFromJson(json);

  /// Converts this Book to JSON
  Map<String, dynamic> toJson() => _$BookToJson(this);

  String get formattedPrice {
    return '$price\$';
  }
}
