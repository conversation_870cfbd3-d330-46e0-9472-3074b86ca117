import 'package:json_annotation/json_annotation.dart';

part 'author.g.dart';

@JsonSerializable()
class Author {
  final int id;

  @Json<PERSON>ey(name: 'first_name')
  final String firstName;

  @<PERSON><PERSON><PERSON>ey(name: 'last_name')
  final String lastName;

  final String country;

  const Author({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.country,
  });

  /// Creates an Author from JSON data
  factory Author.fromJson(Map<String, dynamic> json) => _$AuthorFromJson(json);

  /// Converts this Author to JSON
  Map<String, dynamic> toJson() => _$AuthorToJson(this);

  /// Get the full name of the author
  String get fullName => '$firstName $lastName';

  /// Get a display string for the author with location
  String get displayName => '$fullName ($country)';
}
