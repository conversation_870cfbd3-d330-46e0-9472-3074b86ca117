import 'package:json_annotation/json_annotation.dart';

part 'publisher.g.dart';

@JsonSerializable()
class Publisher {
  final int id;
  final String name;

  const Publisher({
    required this.id,
    required this.name,
  });

  /// Creates a Publisher from JSON data
  factory Publisher.fromJson(Map<String, dynamic> json) => _$PublisherFromJson(json);

  /// Converts this Publisher to JSON
  Map<String, dynamic> toJson() => _$PublisherToJson(this);

  /// Get a display string for the publisher
  String get displayName => name;
}
