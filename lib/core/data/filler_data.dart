import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/publisher.dart';
import 'package:e_library/features/books/data/models/book.dart';

/// Filler data service providing realistic sample data for the e-library
class FillerData {
  // Sample Authors (matching API structure)
  static const List<Map<String, dynamic>> _authorsData = [
    {
      'id': 1,
      'first_name': '<PERSON>',
      'last_name': '<PERSON>',
      'country': 'United Kingdom',
    },
    {
      'id': 2,
      'first_name': '<PERSON>',
      'last_name': 'Orwell',
      'country': 'United Kingdom',
    },
    {
      'id': 3,
      'first_name': '<PERSON>',
      'last_name': '<PERSON>',
      'country': 'United States',
    },
    {
      'id': 4,
      'first_name': '<PERSON>',
      'last_name': '<PERSON>',
      'country': 'Colombia',
    },
    {
      'id': 5,
      'first_name': '<PERSON>',
      'last_name': '<PERSON>',
      'country': 'United States',
    },
    {
      'id': 6,
      'first_name': '<PERSON><PERSON><PERSON>',
      'last_name': '<PERSON><PERSON><PERSON>',
      'country': 'Japan',
    },
    {
      'id': 7,
      'first_name': '<PERSON><PERSON><PERSON><PERSON>',
      'last_name': '<PERSON><PERSON><PERSON>',
      'country': 'Nigeria',
    },
    {
      'id': 8,
      'first_name': 'Paulo',
      'last_name': 'Coelho',
      'country': 'Brazil',
    },
  ];

  // Sample Publishers (matching API structure)
  static const List<Map<String, dynamic>> _publishersData = [
    {
      'id': 1,
      'name': 'Penguin Random House',
    },
    {
      'id': 2,
      'name': 'HarperCollins',
    },
    {
      'id': 3,
      'name': 'Macmillan Publishers',
    },
    {
      'id': 4,
      'name': 'Simon & Schuster',
    },
    {
      'id': 5,
      'name': 'Hachette Book Group',
    },
  ];

  // Sample Books (matching API structure with nested objects)
  static const List<Map<String, dynamic>> _booksData = [
    {
      'id': 1,
      'title': 'Pride and Prejudice',
      'category': 'romance',
      'price': 12.99,
      'publisher_id': 1,
      'author_id': 1,
      'created_at': '2024-01-01T00:00:00.000000Z',
      'updated_at': '2024-01-01T00:00:00.000000Z',
      'author': {
        'id': 1,
        'first_name': 'Jane',
        'last_name': 'Austen',
        'country': 'United Kingdom',
      },
      'publisher': {
        'id': 1,
        'name': 'Penguin Random House',
      },
    },
    {
      'id': 2,
      'title': '1984',
      'category': 'dystopian',
      'price': 14.99,
      'publisher_id': 2,
      'author_id': 2,
      'created_at': '2024-01-02T00:00:00.000000Z',
      'updated_at': '2024-01-02T00:00:00.000000Z',
      'author': {
        'id': 2,
        'first_name': 'George',
        'last_name': 'Orwell',
        'country': 'United Kingdom',
      },
      'publisher': {
        'id': 2,
        'name': 'HarperCollins',
      },
    },
    {
      'id': 3,
      'title': 'Animal Farm',
      'category': 'politics',
      'price': 10.99,
      'publisher_id': 2,
      'author_id': 2,
      'created_at': '2024-01-03T00:00:00.000000Z',
      'updated_at': '2024-01-03T00:00:00.000000Z',
      'author': {
        'id': 2,
        'first_name': 'George',
        'last_name': 'Orwell',
        'country': 'United Kingdom',
      },
      'publisher': {
        'id': 2,
        'name': 'HarperCollins',
      },
    },
    {
      'id': 4,
      'title': 'To Kill a Mockingbird',
      'category': 'fiction',
      'price': 13.99,
      'publisher_id': 3,
      'author_id': 3,
      'created_at': '2024-01-04T00:00:00.000000Z',
      'updated_at': '2024-01-04T00:00:00.000000Z',
      'author': {
        'id': 3,
        'first_name': 'Harper',
        'last_name': 'Lee',
        'country': 'United States',
      },
      'publisher': {
        'id': 3,
        'name': 'Macmillan Publishers',
      },
    },
    {
      'id': 5,
      'title': 'One Hundred Years of Solitude',
      'category': 'fantasy',
      'price': 16.99,
      'publisher_id': 4,
      'author_id': 4,
      'created_at': '2024-01-05T00:00:00.000000Z',
      'updated_at': '2024-01-05T00:00:00.000000Z',
      'author': {
        'id': 4,
        'first_name': 'Gabriel',
        'last_name': 'García Márquez',
        'country': 'Colombia',
      },
      'publisher': {
        'id': 4,
        'name': 'Simon & Schuster',
      },
    },
    {
      'id': 6,
      'title': 'Beloved',
      'category': 'drama',
      'price': 15.99,
      'publisher_id': 5,
      'author_id': 5,
      'created_at': '2024-01-06T00:00:00.000000Z',
      'updated_at': '2024-01-06T00:00:00.000000Z',
      'author': {
        'id': 5,
        'first_name': 'Toni',
        'last_name': 'Morrison',
        'country': 'United States',
      },
      'publisher': {
        'id': 5,
        'name': 'Hachette Book Group',
      },
    },
    {
      'id': 7,
      'title': 'Norwegian Wood',
      'category': 'fiction',
      'price': 14.99,
      'publisher_id': 1,
      'author_id': 6,
      'created_at': '2024-01-07T00:00:00.000000Z',
      'updated_at': '2024-01-07T00:00:00.000000Z',
      'author': {
        'id': 6,
        'first_name': 'Haruki',
        'last_name': 'Murakami',
        'country': 'Japan',
      },
      'publisher': {
        'id': 1,
        'name': 'Penguin Random House',
      },
    },
    {
      'id': 8,
      'title': 'Americanah',
      'category': 'fiction',
      'price': 13.99,
      'publisher_id': 2,
      'author_id': 7,
      'created_at': '2024-01-08T00:00:00.000000Z',
      'updated_at': '2024-01-08T00:00:00.000000Z',
      'author': {
        'id': 7,
        'first_name': 'Chimamanda',
        'last_name': 'Ngozi Adichie',
        'country': 'Nigeria',
      },
      'publisher': {
        'id': 2,
        'name': 'HarperCollins',
      },
    },
    {
      'id': 9,
      'title': 'The Alchemist',
      'category': 'self_help',
      'price': 11.99,
      'publisher_id': 3,
      'author_id': 8,
      'created_at': '2024-01-09T00:00:00.000000Z',
      'updated_at': '2024-01-09T00:00:00.000000Z',
      'author': {
        'id': 8,
        'first_name': 'Paulo',
        'last_name': 'Coelho',
        'country': 'Brazil',
      },
      'publisher': {
        'id': 3,
        'name': 'Macmillan Publishers',
      },
    },
    {
      'id': 10,
      'title': 'Sense and Sensibility',
      'category': 'romance',
      'price': 12.99,
      'publisher_id': 1,
      'author_id': 1,
      'created_at': '2024-01-10T00:00:00.000000Z',
      'updated_at': '2024-01-10T00:00:00.000000Z',
      'author': {
        'id': 1,
        'first_name': 'Jane',
        'last_name': 'Austen',
        'country': 'United Kingdom',
      },
      'publisher': {
        'id': 1,
        'name': 'Penguin Random House',
      },
    },
  ];

  /// Get all sample authors
  static List<Author> get authors {
    return _authorsData.map((data) => Author.fromJson(data)).toList();
  }

  /// Get all sample publishers
  static List<Publisher> get publishers {
    return _publishersData.map((data) => Publisher.fromJson(data)).toList();
  }

  /// Get all sample books
  static List<Book> get books {
    return _booksData.map((data) => Book.fromJson(data)).toList();
  }

  /// Get author by ID
  static Author? getAuthorById(int id) {
    try {
      final authorData = _authorsData.firstWhere((author) => author['id'] == id);
      return Author.fromJson(authorData);
    } catch (e) {
      return null;
    }
  }

  /// Get publisher by ID
  static Publisher? getPublisherById(int id) {
    try {
      final publisherData = _publishersData.firstWhere((publisher) => publisher['id'] == id);
      return Publisher.fromJson(publisherData);
    } catch (e) {
      return null;
    }
  }

  /// Get books by author ID
  static List<Book> getBooksByAuthorId(int authorId) {
    return books.where((book) => book.authorId == authorId).toList();
  }

  /// Get books by publisher ID
  static List<Book> getBooksByPublisherId(int publisherId) {
    return books.where((book) => book.publisherId == publisherId).toList();
  }

  /// Get books by category
  static List<Book> getBooksByCategory(String category) {
    return books.where((book) => book.category.toLowerCase() == category.toLowerCase()).toList();
  }

  /// Get all unique categories
  static List<String> get categories {
    return books.map((book) => book.category).toSet().toList()..sort();
  }
}
