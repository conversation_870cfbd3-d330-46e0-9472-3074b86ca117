import 'package:e_library/features/books/data/models/author.dart';
import 'package:e_library/features/books/data/models/publisher.dart';
import 'package:e_library/features/books/data/models/book.dart';

/// Filler data service providing realistic sample data for the e-library
class FillerData {
  // Sample Authors
  static const List<Map<String, dynamic>> _authorsData = [
    {
      'id': 1,
      'first_name': '<PERSON>',
      'last_name': '<PERSON>',
      'country': 'United Kingdom',
      'city': 'Winchester',
      'address': '8 College Street',
      'created_at': '2024-01-01T00:00:00.000000Z',
      'updated_at': '2024-01-01T00:00:00.000000Z',
    },
    {
      'id': 2,
      'first_name': '<PERSON>',
      'last_name': 'Orwell',
      'country': 'United Kingdom',
      'city': 'London',
      'address': '27B Canonbury Square',
      'created_at': '2024-01-02T00:00:00.000000Z',
      'updated_at': '2024-01-02T00:00:00.000000Z',
    },
    {
      'id': 3,
      'first_name': '<PERSON>',
      'last_name': '<PERSON>',
      'country': 'United States',
      'city': 'Monroeville',
      'address': '82 West Avenue',
      'created_at': '2024-01-03T00:00:00.000000Z',
      'updated_at': '2024-01-03T00:00:00.000000Z',
    },
    {
      'id': 4,
      'first_name': 'Gabriel',
      'last_name': 'García Márquez',
      'country': 'Colombia',
      'city': 'Cartagena',
      'address': 'Calle de la Universidad',
      'created_at': '2024-01-04T00:00:00.000000Z',
      'updated_at': '2024-01-04T00:00:00.000000Z',
    },
    {
      'id': 5,
      'first_name': 'Toni',
      'last_name': 'Morrison',
      'country': 'United States',
      'city': 'Princeton',
      'address': '185 Nassau Street',
      'created_at': '2024-01-05T00:00:00.000000Z',
      'updated_at': '2024-01-05T00:00:00.000000Z',
    },
    {
      'id': 6,
      'first_name': 'Haruki',
      'last_name': 'Murakami',
      'country': 'Japan',
      'city': 'Tokyo',
      'address': '1-1-1 Shibuya',
      'created_at': '2024-01-06T00:00:00.000000Z',
      'updated_at': '2024-01-06T00:00:00.000000Z',
    },
    {
      'id': 7,
      'first_name': 'Chimamanda',
      'last_name': 'Ngozi Adichie',
      'country': 'Nigeria',
      'city': 'Lagos',
      'address': 'Victoria Island',
      'created_at': '2024-01-07T00:00:00.000000Z',
      'updated_at': '2024-01-07T00:00:00.000000Z',
    },
    {
      'id': 8,
      'first_name': 'Paulo',
      'last_name': 'Coelho',
      'country': 'Brazil',
      'city': 'Rio de Janeiro',
      'address': 'Copacabana Beach',
      'created_at': '2024-01-08T00:00:00.000000Z',
      'updated_at': '2024-01-08T00:00:00.000000Z',
    },
  ];

  // Sample Publishers
  static const List<Map<String, dynamic>> _publishersData = [
    {
      'id': 1,
      'name': 'Penguin Random House',
      'city': 'New York',
      'created_at': '2024-01-01T00:00:00.000000Z',
      'updated_at': '2024-01-01T00:00:00.000000Z',
    },
    {
      'id': 2,
      'name': 'HarperCollins',
      'city': 'London',
      'created_at': '2024-01-02T00:00:00.000000Z',
      'updated_at': '2024-01-02T00:00:00.000000Z',
    },
    {
      'id': 3,
      'name': 'Macmillan Publishers',
      'city': 'London',
      'created_at': '2024-01-03T00:00:00.000000Z',
      'updated_at': '2024-01-03T00:00:00.000000Z',
    },
    {
      'id': 4,
      'name': 'Simon & Schuster',
      'city': 'New York',
      'created_at': '2024-01-04T00:00:00.000000Z',
      'updated_at': '2024-01-04T00:00:00.000000Z',
    },
    {
      'id': 5,
      'name': 'Hachette Book Group',
      'city': 'New York',
      'created_at': '2024-01-05T00:00:00.000000Z',
      'updated_at': '2024-01-05T00:00:00.000000Z',
    },
  ];

  // Sample Books
  static const List<Map<String, dynamic>> _booksData = [
    {
      'id': 1,
      'title': 'Pride and Prejudice',
      'category': 'Romance',
      'price': '12.99',
      'publisher_id': 1,
      'author_id': 1,
      'created_at': '2024-01-01T00:00:00.000000Z',
      'updated_at': '2024-01-01T00:00:00.000000Z',
    },
    {
      'id': 2,
      'title': '1984',
      'category': 'Dystopian Fiction',
      'price': '14.99',
      'publisher_id': 2,
      'author_id': 2,
      'created_at': '2024-01-02T00:00:00.000000Z',
      'updated_at': '2024-01-02T00:00:00.000000Z',
    },
    {
      'id': 3,
      'title': 'Animal Farm',
      'category': 'Political Satire',
      'price': '10.99',
      'publisher_id': 2,
      'author_id': 2,
      'created_at': '2024-01-03T00:00:00.000000Z',
      'updated_at': '2024-01-03T00:00:00.000000Z',
    },
    {
      'id': 4,
      'title': 'To Kill a Mockingbird',
      'category': 'Literary Fiction',
      'price': '13.99',
      'publisher_id': 3,
      'author_id': 3,
      'created_at': '2024-01-04T00:00:00.000000Z',
      'updated_at': '2024-01-04T00:00:00.000000Z',
    },
    {
      'id': 5,
      'title': 'One Hundred Years of Solitude',
      'category': 'Magical Realism',
      'price': '16.99',
      'publisher_id': 4,
      'author_id': 4,
      'created_at': '2024-01-05T00:00:00.000000Z',
      'updated_at': '2024-01-05T00:00:00.000000Z',
    },
    {
      'id': 6,
      'title': 'Beloved',
      'category': 'Historical Fiction',
      'price': '15.99',
      'publisher_id': 5,
      'author_id': 5,
      'created_at': '2024-01-06T00:00:00.000000Z',
      'updated_at': '2024-01-06T00:00:00.000000Z',
    },
    {
      'id': 7,
      'title': 'Norwegian Wood',
      'category': 'Contemporary Fiction',
      'price': '14.99',
      'publisher_id': 1,
      'author_id': 6,
      'created_at': '2024-01-07T00:00:00.000000Z',
      'updated_at': '2024-01-07T00:00:00.000000Z',
    },
    {
      'id': 8,
      'title': 'Americanah',
      'category': 'Contemporary Fiction',
      'price': '13.99',
      'publisher_id': 2,
      'author_id': 7,
      'created_at': '2024-01-08T00:00:00.000000Z',
      'updated_at': '2024-01-08T00:00:00.000000Z',
    },
    {
      'id': 9,
      'title': 'The Alchemist',
      'category': 'Philosophical Fiction',
      'price': '11.99',
      'publisher_id': 3,
      'author_id': 8,
      'created_at': '2024-01-09T00:00:00.000000Z',
      'updated_at': '2024-01-09T00:00:00.000000Z',
    },
    {
      'id': 10,
      'title': 'Sense and Sensibility',
      'category': 'Romance',
      'price': '12.99',
      'publisher_id': 1,
      'author_id': 1,
      'created_at': '2024-01-10T00:00:00.000000Z',
      'updated_at': '2024-01-10T00:00:00.000000Z',
    },
  ];

  /// Get all sample authors
  static List<Author> get authors {
    return _authorsData.map((data) => Author.fromJson(data)).toList();
  }

  /// Get all sample publishers
  static List<Publisher> get publishers {
    return _publishersData.map((data) => Publisher.fromJson(data)).toList();
  }

  /// Get all sample books
  static List<Book> get books {
    return _booksData.map((data) => Book.fromJson(data)).toList();
  }

  /// Get author by ID
  static Author? getAuthorById(int id) {
    try {
      final authorData = _authorsData.firstWhere((author) => author['id'] == id);
      return Author.fromJson(authorData);
    } catch (e) {
      return null;
    }
  }

  /// Get publisher by ID
  static Publisher? getPublisherById(int id) {
    try {
      final publisherData = _publishersData.firstWhere((publisher) => publisher['id'] == id);
      return Publisher.fromJson(publisherData);
    } catch (e) {
      return null;
    }
  }

  /// Get books by author ID
  static List<Book> getBooksByAuthorId(int authorId) {
    return books.where((book) => book.authorId == authorId).toList();
  }

  /// Get books by publisher ID
  static List<Book> getBooksByPublisherId(int publisherId) {
    return books.where((book) => book.publisherId == publisherId).toList();
  }

  /// Get books by category
  static List<Book> getBooksByCategory(String category) {
    return books.where((book) => book.category.toLowerCase() == category.toLowerCase()).toList();
  }

  /// Get all unique categories
  static List<String> get categories {
    return books.map((book) => book.category).toSet().toList()..sort();
  }
}
